#!/usr/bin/env python3
"""
XML Key Verification and Reporting Tool
Analyzes XML files to verify master/foreign key relationships and generate reports
"""

import xml.etree.ElementTree as ET
import pandas as pd
import os
from collections import defaultdict, Counter
import json
from datetime import datetime

class XMLKeyVerifier:
    def __init__(self, xml_directory="xmlfiles"):
        self.xml_directory = xml_directory
        self.data = {}
        self.key_relationships = {}
        self.integrity_issues = []
        
    def load_xml_files(self):
        """Load all XML files and extract data"""
        print("Loading XML files...")
        
        # Load RefData (Reference data with communities)
        self.load_refdata()
        
        # Load Students data
        self.load_students()
        
        # Load Grades (MoyenneCC)
        self.load_grades()
        
        # Load Bulletins
        self.load_bulletins()
        
        print(f"Loaded {len(self.data)} datasets")
    
    def load_refdata(self):
        """Load reference data (communities, establishments, etc.)"""
        file_path = os.path.join(self.xml_directory, "Export_RefData_24022025202004.xml")
        if not os.path.exists(file_path):
            print(f"Warning: {file_path} not found")
            return
            
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # Extract communities
        communities = []
        for com in root.findall('.//{http://tempuri.org/SgsDataExportDs.xsd}z_com'):
            community = {}
            for child in com:
                tag = child.tag.replace('{http://tempuri.org/SgsDataExportDs.xsd}', '')
                community[tag] = child.text
            communities.append(community)
        
        self.data['communities'] = pd.DataFrame(communities)
        print(f"Loaded {len(communities)} communities")
    
    def load_students(self):
        """Load student data"""
        file_path = os.path.join(self.xml_directory, "Export_Eleve_24022025201959.xml")
        if not os.path.exists(file_path):
            print(f"Warning: {file_path} not found")
            return
            
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        students = []
        for eleve in root.findall('.//{http://tempuri.org/SgsDataExportDs.xsd}EleveSco'):
            student = {}
            for child in eleve:
                tag = child.tag.replace('{http://tempuri.org/SgsDataExportDs.xsd}', '')
                student[tag] = child.text
            students.append(student)
        
        self.data['students'] = pd.DataFrame(students)
        print(f"Loaded {len(students)} students")
    
    def load_grades(self):
        """Load grades data (MoyenneCC)"""
        file_path = os.path.join(self.xml_directory, "Export_MoyenneCC_24022025202001.xml")
        if not os.path.exists(file_path):
            print(f"Warning: {file_path} not found")
            return
            
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        grades = []
        for note in root.findall('.//{http://tempuri.org/SgsDataExportDs.xsd}MoyenneNoteCC'):
            grade = {}
            for child in note:
                tag = child.tag.replace('{http://tempuri.org/SgsDataExportDs.xsd}', '')
                grade[tag] = child.text
            grades.append(grade)
        
        self.data['grades'] = pd.DataFrame(grades)
        print(f"Loaded {len(grades)} grade records")
    
    def load_bulletins(self):
        """Load bulletin data"""
        file_path = os.path.join(self.xml_directory, "Export_Bulletin_24022025202003.xml")
        if not os.path.exists(file_path):
            print(f"Warning: {file_path} not found")
            return
            
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        bulletins = []
        for bulletin in root.findall('.//{http://tempuri.org/SgsDataExportDs.xsd}BulletinEleve'):
            bulletin_data = {}
            for child in bulletin:
                tag = child.tag.replace('{http://tempuri.org/SgsDataExportDs.xsd}', '')
                bulletin_data[tag] = child.text
            bulletins.append(bulletin_data)
        
        self.data['bulletins'] = pd.DataFrame(bulletins)
        print(f"Loaded {len(bulletins)} bulletin records")
    
    def verify_key_integrity(self):
        """Verify master and foreign key relationships"""
        print("\n=== KEY INTEGRITY VERIFICATION ===")
        
        # Check if we have the required datasets
        required_datasets = ['students', 'grades', 'bulletins']
        missing_datasets = [ds for ds in required_datasets if ds not in self.data]
        
        if missing_datasets:
            print(f"Warning: Missing datasets: {missing_datasets}")
            return
        
        # 1. Verify codeEleve relationships
        self.verify_student_keys()
        
        # 2. Verify establishment codes
        self.verify_establishment_keys()
        
        # 3. Check for duplicate primary keys
        self.check_duplicate_keys()
        
        # 4. Check data consistency
        self.check_data_consistency()
    
    def verify_student_keys(self):
        """Verify student code relationships across tables"""
        print("\n1. Verifying Student Code (codeEleve) relationships:")
        
        # Get unique student codes from each dataset
        students_codes = set(self.data['students']['codeEleve'].dropna())
        grades_codes = set(self.data['grades']['codeEleve'].dropna())
        bulletins_codes = set(self.data['bulletins']['codeEleve'].dropna())
        
        print(f"   - Students table: {len(students_codes)} unique codes")
        print(f"   - Grades table: {len(grades_codes)} unique codes")
        print(f"   - Bulletins table: {len(bulletins_codes)} unique codes")
        
        # Check orphaned records
        orphaned_grades = grades_codes - students_codes
        orphaned_bulletins = bulletins_codes - students_codes
        
        if orphaned_grades:
            print(f"   ⚠️  WARNING: {len(orphaned_grades)} orphaned student codes in grades")
            self.integrity_issues.append({
                'type': 'orphaned_foreign_key',
                'table': 'grades',
                'field': 'codeEleve',
                'count': len(orphaned_grades),
                'examples': list(orphaned_grades)[:5]
            })
        
        if orphaned_bulletins:
            print(f"   ⚠️  WARNING: {len(orphaned_bulletins)} orphaned student codes in bulletins")
            self.integrity_issues.append({
                'type': 'orphaned_foreign_key',
                'table': 'bulletins',
                'field': 'codeEleve',
                'count': len(orphaned_bulletins),
                'examples': list(orphaned_bulletins)[:5]
            })
        
        if not orphaned_grades and not orphaned_bulletins:
            print("   ✅ All student codes have valid references")
    
    def verify_establishment_keys(self):
        """Verify establishment codes"""
        print("\n2. Verifying Establishment Codes (CD_ETAB):")
        
        if 'students' not in self.data:
            return
            
        etab_codes = self.data['students']['CD_ETAB'].dropna().unique()
        print(f"   - Found {len(etab_codes)} unique establishment codes")
        
        # Count students per establishment
        etab_counts = self.data['students']['CD_ETAB'].value_counts()
        print(f"   - Top 5 establishments by student count:")
        for etab, count in etab_counts.head().items():
            print(f"     {etab}: {count} students")
    
    def check_duplicate_keys(self):
        """Check for duplicate primary keys"""
        print("\n3. Checking for duplicate primary keys:")
        
        # Check student codes
        if 'students' in self.data:
            student_duplicates = self.data['students']['codeEleve'].duplicated().sum()
            if student_duplicates > 0:
                print(f"   ⚠️  WARNING: {student_duplicates} duplicate student codes found")
                self.integrity_issues.append({
                    'type': 'duplicate_primary_key',
                    'table': 'students',
                    'field': 'codeEleve',
                    'count': student_duplicates
                })
            else:
                print("   ✅ No duplicate student codes")
        
        # Check community codes
        if 'communities' in self.data:
            community_duplicates = self.data['communities']['cd_com'].duplicated().sum()
            if community_duplicates > 0:
                print(f"   ⚠️  WARNING: {community_duplicates} duplicate community codes found")
                self.integrity_issues.append({
                    'type': 'duplicate_primary_key',
                    'table': 'communities',
                    'field': 'cd_com',
                    'count': community_duplicates
                })
            else:
                print("   ✅ No duplicate community codes")
    
    def check_data_consistency(self):
        """Check data consistency across tables"""
        print("\n4. Checking data consistency:")
        
        if 'grades' in self.data and 'bulletins' in self.data:
            # Check if all students with grades have bulletins
            students_with_grades = set(self.data['grades']['codeEleve'].dropna())
            students_with_bulletins = set(self.data['bulletins']['codeEleve'].dropna())
            
            missing_bulletins = students_with_grades - students_with_bulletins
            if missing_bulletins:
                print(f"   ⚠️  WARNING: {len(missing_bulletins)} students have grades but no bulletins")
                self.integrity_issues.append({
                    'type': 'missing_related_data',
                    'description': 'Students with grades but no bulletins',
                    'count': len(missing_bulletins),
                    'examples': list(missing_bulletins)[:5]
                })
            else:
                print("   ✅ All students with grades have bulletins")

    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        print("\n" + "="*60)
        print("           COMPREHENSIVE DATA SUMMARY REPORT")
        print("="*60)

        report = {
            'timestamp': datetime.now().isoformat(),
            'datasets': {},
            'key_integrity': {
                'issues_found': len(self.integrity_issues),
                'issues': self.integrity_issues
            },
            'statistics': {}
        }

        # Dataset summaries
        for name, df in self.data.items():
            report['datasets'][name] = {
                'record_count': len(df),
                'columns': list(df.columns),
                'null_counts': df.isnull().sum().to_dict()
            }

            print(f"\n{name.upper()} Dataset:")
            print(f"  - Records: {len(df):,}")
            print(f"  - Columns: {len(df.columns)}")

            # Show null value statistics
            null_counts = df.isnull().sum()
            if null_counts.sum() > 0:
                print("  - Null values:")
                for col, count in null_counts[null_counts > 0].items():
                    print(f"    {col}: {count}")

        return report

    def generate_student_performance_report(self):
        """Generate student performance analysis"""
        if 'students' not in self.data or 'grades' not in self.data or 'bulletins' not in self.data:
            print("Cannot generate performance report - missing required data")
            return

        print("\n" + "="*60)
        print("           STUDENT PERFORMANCE REPORT")
        print("="*60)

        # Merge student data with bulletins
        student_performance = self.data['students'].merge(
            self.data['bulletins'],
            on='codeEleve',
            how='inner'
        )

        print(f"\nPerformance Summary for {len(student_performance)} students:")

        # Convert grades to numeric
        student_performance['MoyenneSession_num'] = pd.to_numeric(
            student_performance['MoyenneSession'].str.replace(',', '.'),
            errors='coerce'
        )

        # Basic statistics
        avg_grade = student_performance['MoyenneSession_num'].mean()
        median_grade = student_performance['MoyenneSession_num'].median()
        min_grade = student_performance['MoyenneSession_num'].min()
        max_grade = student_performance['MoyenneSession_num'].max()

        print(f"  - Average Grade: {avg_grade:.2f}")
        print(f"  - Median Grade: {median_grade:.2f}")
        print(f"  - Minimum Grade: {min_grade:.2f}")
        print(f"  - Maximum Grade: {max_grade:.2f}")

        # Grade distribution
        print(f"\nGrade Distribution:")
        grade_ranges = [
            (0, 5, "Very Poor (0-5)"),
            (5, 10, "Poor (5-10)"),
            (10, 12, "Average (10-12)"),
            (12, 14, "Good (12-14)"),
            (14, 16, "Very Good (14-16)"),
            (16, 20, "Excellent (16-20)")
        ]

        for min_g, max_g, label in grade_ranges:
            count = len(student_performance[
                (student_performance['MoyenneSession_num'] >= min_g) &
                (student_performance['MoyenneSession_num'] < max_g)
            ])
            percentage = (count / len(student_performance)) * 100
            print(f"  - {label}: {count} students ({percentage:.1f}%)")

        # Performance by establishment
        print(f"\nTop 10 Establishments by Average Performance:")
        etab_performance = student_performance.groupby('CD_ETAB').agg({
            'MoyenneSession_num': ['mean', 'count']
        }).round(2)
        etab_performance.columns = ['avg_grade', 'student_count']
        etab_performance = etab_performance[etab_performance['student_count'] >= 5]  # At least 5 students
        etab_performance = etab_performance.sort_values('avg_grade', ascending=False)

        for etab, row in etab_performance.head(10).iterrows():
            print(f"  - {etab}: {row['avg_grade']:.2f} avg ({int(row['student_count'])} students)")

        return student_performance

    def generate_subject_analysis_report(self):
        """Generate subject-wise analysis"""
        if 'grades' not in self.data:
            print("Cannot generate subject analysis - missing grades data")
            return

        print("\n" + "="*60)
        print("           SUBJECT ANALYSIS REPORT")
        print("="*60)

        grades_df = self.data['grades'].copy()

        # Convert grades to numeric
        grades_df['Note_num'] = pd.to_numeric(
            grades_df['Note'].str.replace(',', '.'),
            errors='coerce'
        )

        # Subject statistics
        subject_stats = grades_df.groupby('cd_matiere').agg({
            'Note_num': ['mean', 'std', 'count', 'min', 'max']
        }).round(2)
        subject_stats.columns = ['avg_grade', 'std_dev', 'student_count', 'min_grade', 'max_grade']
        subject_stats = subject_stats.sort_values('avg_grade', ascending=False)

        print(f"\nSubject Performance Summary ({len(subject_stats)} subjects):")
        print(f"{'Subject':<10} {'Avg':<6} {'StdDev':<7} {'Students':<9} {'Min':<5} {'Max':<5}")
        print("-" * 50)

        for subject, row in subject_stats.head(15).iterrows():
            print(f"{subject:<10} {row['avg_grade']:<6} {row['std_dev']:<7} {int(row['student_count']):<9} {row['min_grade']:<5} {row['max_grade']:<5}")

        # Identify challenging subjects (low average, high std dev)
        challenging_subjects = subject_stats[
            (subject_stats['avg_grade'] < subject_stats['avg_grade'].median()) &
            (subject_stats['std_dev'] > subject_stats['std_dev'].median())
        ]

        if len(challenging_subjects) > 0:
            print(f"\nChallenging Subjects (Low avg, High variation):")
            for subject, row in challenging_subjects.head(5).iterrows():
                print(f"  - Subject {subject}: {row['avg_grade']:.2f} avg, {row['std_dev']:.2f} std dev")

        return subject_stats

    def export_reports(self, output_dir="reports"):
        """Export all reports to files"""
        os.makedirs(output_dir, exist_ok=True)

        print(f"\n" + "="*60)
        print(f"           EXPORTING REPORTS TO {output_dir}/")
        print("="*60)

        # Export summary report as JSON
        summary_report = self.generate_summary_report()
        with open(os.path.join(output_dir, "summary_report.json"), 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, indent=2, ensure_ascii=False)
        print("✅ Summary report exported to summary_report.json")

        # Export student performance to CSV
        if 'students' in self.data and 'bulletins' in self.data:
            student_perf = self.generate_student_performance_report()
            student_perf.to_csv(os.path.join(output_dir, "student_performance.csv"), index=False, encoding='utf-8')
            print("✅ Student performance exported to student_performance.csv")

        # Export subject analysis to CSV
        if 'grades' in self.data:
            subject_stats = self.generate_subject_analysis_report()
            subject_stats.to_csv(os.path.join(output_dir, "subject_analysis.csv"), encoding='utf-8')
            print("✅ Subject analysis exported to subject_analysis.csv")

        # Export integrity issues
        if self.integrity_issues:
            with open(os.path.join(output_dir, "integrity_issues.json"), 'w', encoding='utf-8') as f:
                json.dump(self.integrity_issues, f, indent=2, ensure_ascii=False)
            print("✅ Integrity issues exported to integrity_issues.json")

        # Export raw data summaries
        for name, df in self.data.items():
            df.to_csv(os.path.join(output_dir, f"{name}_data.csv"), index=False, encoding='utf-8')
            print(f"✅ {name} data exported to {name}_data.csv")

def main():
    """Main execution function"""
    print("XML Key Verification and Reporting Tool")
    print("=" * 50)

    # Initialize verifier
    verifier = XMLKeyVerifier()

    # Load XML files
    verifier.load_xml_files()

    # Verify key integrity
    verifier.verify_key_integrity()

    # Generate reports
    verifier.generate_summary_report()
    verifier.generate_student_performance_report()
    verifier.generate_subject_analysis_report()

    # Export all reports
    verifier.export_reports()

    print(f"\n" + "="*60)
    print("           ANALYSIS COMPLETE")
    print("="*60)
    print("Check the 'reports' directory for detailed output files.")

if __name__ == "__main__":
    main()
